// addHumanInTheLoop.ts

import { BaseTool, Tool, ToolArgsSchema } from "@langchain/core/tools";
import { RunnableConfig } from "@langchain/core/runnables";
import {
  interrupt,
  HumanInterruptConfig,
  HumanInterrupt,
} from "langgraph/types";
import { tool as createTool } from "@langchain/core/tools";

/**
 * Wrap a tool to support human-in-the-loop review.
 * Compatible with LangGraph.js and LangChain.js tools.
 */
export function addHumanInTheLoop<TArgs extends object, TResult>(
  tool:
    | BaseTool<TArgs, TResult>
    | ((args: TArgs, config: RunnableConfig) => Promise<TResult> | TResult),
  options: {
    interruptConfig?: HumanInterruptConfig;
    name?: string;
    description?: string;
    argsSchema?: ToolArgsSchema<TArgs>;
  } = {}
): BaseTool<TArgs, TResult> {
  const interruptConfig: HumanInterruptConfig = options.interruptConfig ?? {
    allow_accept: true,
    allow_edit: true,
    allow_respond: true,
  };

  // If the tool is a function, wrap it as a Tool
  let baseTool: BaseTool<TArgs, TResult>;
  if (typeof tool === "function") {
    baseTool = createTool<TArgs, TResult>({
      name: options.name ?? tool.name ?? "anonymous_tool",
      description:
        options.description ?? "A tool with human-in-the-loop review.",
      argsSchema: options.argsSchema,
      func: tool,
    });
  } else {
    baseTool = tool;
  }

  // Create a new tool that wraps the original with interrupt logic
  return createTool<TArgs, TResult>({
    name: baseTool.name,
    description: baseTool.description,
    argsSchema: baseTool.argsSchema,
    async func(args: TArgs, config: RunnableConfig): Promise<TResult> {
      const request: HumanInterrupt = {
        action_request: {
          action: baseTool.name,
          args,
        },
        config: interruptConfig,
        description: "Please review the tool call",
      };

      // Await human review via interrupt
      const [response] = await interrupt([request]);

      if (response.type === "accept") {
        // Approve the tool call
        return baseTool.invoke(args, config);
      } else if (response.type === "edit") {
        // Update tool call args
        const newArgs = response.args.args as TArgs;
        return baseTool.invoke(newArgs, config);
      } else if (response.type === "response") {
        // Respond to the LLM with user feedback
        return response.args as TResult;
      } else {
        throw new Error(
          `Unsupported interrupt response type: ${response.type}`
        );
      }
    },
  });
}
